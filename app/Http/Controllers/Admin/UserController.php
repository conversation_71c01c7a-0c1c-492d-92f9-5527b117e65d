<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(){
        $user = User::query()
            ->select('id','username','email', 'phone', 'is_active', 'created_at')
            ->orderBy('id','desc')
            ->paginate(10);
        return view('admin.users.index',[
            'users' => $user,
        ]);
    }
}
