<?php

namespace Database\Seeders;

use App\Models\User;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
         User::factory(10)->create();

        User::query()->create([
            'full_name' => '<PERSON>',
            'username' => 'vannguyentrung',
            'email' => '<EMAIL>',
            'password' => \Hash::make('password'),
            'phone' => '0374407152',
            'is_admin' => true,
            'is_active' => true,
        ]);
    }
}
