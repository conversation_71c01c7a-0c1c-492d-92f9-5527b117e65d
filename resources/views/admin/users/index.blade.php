<x-admin-layout>
    <x-slot name="header">
        <h2 class="font-medium text-base text-gray-800 dark:text-gray-200 leading-tight">
            Qu<PERSON><PERSON> lý thành viên
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="overflow-x-auto p-2">
                    <table class="min-w-full border border-gray-200 divide-y divide-gray-200">
                        <thead class="bg-gray-100">
                        <tr>
                            <th class="table-th">ID</th>
                            <th class="table-th">Tên</th>
                            <th class="table-th">Trạng thái</th>
                            <th class="table-th">Ngày tạo</th>
                            <th class="table-th">Thao tác</th>
                        </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                        @foreach($users as $item)
                            <tr class="hover:bg-gray-50">
                                <td class="table-td">{{ $item->id }}</td>
                                <td class="table-td">
                                    <a href="#" class="block text-blue-500">{{ $item->username }}</a>
                                    <span class="block">{{ $item->email }}</span>
                                    <span class="block">{{ $item->phone }}</span>
                                </td>
                                <td class="table-td">
                                    <label class="inline-flex items-center cursor-pointer">
                                        <input type="checkbox" value="{{ $item->id }}" class="sr-only peer" {{ $item->is_active ? 'checked' : '' }}>
                                        <div
                                            class="relative w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600"></div>
                                    </label>
                                </td>
                                <td class="table-td">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-600 border border-green-500">
                                        {{ $item->created_at->format('d-m-Y') }}
                                    </span>
                                </td>
                                <td class="table-td">
                                    <div class="flex items-center space-x-4">
                                        <a href="#" class="text-green-500 cursor-pointer">
                                            <i class="fa fa-eye text-base"></i>
                                        </a>
                                        <a href="#" class="text-blue-500 cursor-pointer">
                                            <i class="fa fa-edit text-base"></i>
                                        </a>
                                        <a href="#" class="text-red-500 cursor-pointer">
                                            <i class="fa fa-trash-alt text-base"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $users->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
